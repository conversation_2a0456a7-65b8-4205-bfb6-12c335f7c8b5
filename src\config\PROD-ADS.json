{"DEPLOY": {"cloud": "HIS", "infra": "ADS", "enable_doc": false, "service_name": "/mmae-pyxis", "uri_prefix": "/mmae-pyxis", "config_server": {"url": "http://appconfig.huawei.com/ConfigCenter/services/saasConfigcenterGetConfig", "app_id": "com.huawei.bi.aiservice.anaportal", "du": "scaffolding", "environment": "kwe_prod", "region": "kwe", "version": "1.0", "config_parts": ["替换成生产token解密要的config parts 1", "替换成生产token解密要的config parts 2"]}, "api_gateway": {"authorization": {"url": "http://w3cloud.huawei.com/ApiCommonQuery/appToken/getRestAppDynamicToken"}, "authentication": {"sgov_token": "http://w3cloud.huawei.com/ApiCommonQuery/appToken/validateToken", "sgov_uri": "http://w3cloud.huawei.com/ApiCommonQuery/appToken/authorizeByURI"}}}, "LOGGER": {"name": "<PERSON><PERSON>", "output_directory": "/applog/logs/app.log", "log_to_file": true, "log_format": "%(asctime)s | PID-%(process)d-%(threadName)s-%(thread)d | %(name)s | %(filename)s:%(lineno)d | %(levelname)s | %(message)s", "level": "INFO", "max_file_size": 102400000, "backup_count": 5}, "RDB": {"pg_demo": {"datasource_name": "pyxis.pg", "tables": ["iris"], "echo": false}}, "OBS": {"demo_bucket": {"bucket": "fai-mlops-demo", "pool_size": 5, "max_overflow": 5, "pool_timeout": 2, "pool_recycle": 3600, "pool_pre_ping": true}}, "LIMITER": {"storage_uri": null, "strategy": "moving-window", "unit": "minute", "frequency": 10}}