---
version: 2.0
#构建环境
env:
  #image: CloudBuild_MesosSlave_Euler2_5_green_wlcb_20190918_162429_197_img # 镜像名称，暂未使用
  label: BPIT_Build_Default # 构建资源标签
  #resource_class: 4u4g # 构建资源规格，暂未使用
#构建参数定义, 构建脚本可从环境变量中读取使用这些参数
params:
  - name: product
    value: cloudbuild2.0
#构建步骤
steps:
  PRE_BUILD: # 构建准备步骤
    - checkout # 检出当前源码库
    #    path: mainSource  # 下载子路径，可选，如果配置，那么将会把代码下载到子子路径下，如果不配置，那么将会下载到当前路径
    #- gitlab: # 添加依赖的gitlab代码仓库下载
    #    url: https://git-beta-wulan.inhuawei.com/jiffy/s_linuxkernel.git # 代码仓库地址
    #    branch: master # 代码仓库分支
    #    path: linux_kernel # 下载子路径
    #- cmc: # cmc lcrp下载二方依赖插件
    #    dependency: dependency.xml # lcrp需要的dependency.xml相对路径
  BUILD: # 构建执行步骤
    - build_execute: # 执行构建
        command: sh build.sh  # 指定执行的build文件。如果在子目录：子目录名/build.sh
        accelerate: false # 是否启用分布式加速(jiffy)
        #check: true # 是否启用构建检查
        check:
            sourcecheck:  # 构建来源检查
              - project_type: python  # 必配项。当前支持检查maven/npm/python/gomod/gradle仓库来源检查
                project_dir: .  # 工程根目录，必配项。如maven为pom文件所在目录，如npm为package.json文件所在目录
            dependency:
              - tool_type: python    # 必配项。当前支持检查maven/npm/python/gomod
                project_dir: ./   # 工程根目录，必配项。如maven为pom文件所在目录，如npm为package.json文件所在目录
                #settings_xml: $WORKSPACE/settings.xml
                skip_plugin: false
  POST_BUILD:  # 构建后步骤
    - sh:
        command: | #数字签名配置
          signclient "${sub_path}/*.tar.gz" #不同签名包之间用分号分隔
    - artget: #从云龙上传构建cloudArtifact仓库 需要云龙流水线传入serviceId,serviceName,isRelease参数
        artifact_type: cloudartifact  # 仓库类型
        action: push #选填。默认值为push,当上传包数量超过10个时，必填。
        file_path: "package,${sub_path}/*.tar.gz;cms,${sub_path}/*.tar.gz.cms" #上传包的路径是相对路径，相对于workspace
        version_output_path: .
    - version_set