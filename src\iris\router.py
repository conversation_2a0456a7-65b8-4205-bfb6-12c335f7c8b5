from enum import Enum

from fastapi import APIRouter, Depends, Query, Request, status
from sqlalchemy.orm import Session

from pyxis.utils.obs.his_obs_client import HisObsClient
from src.iris import service
from src.iris import template
from src.utils.resource_loader import <PERSON>NFIG, RDB_POOLS
from src.utils.response_generator import response_generator

router = APIRouter(
    prefix=f"{CONFIG.deploy.service_name}/iris",
    tags=["iris_demo_service"],
)


class IrisStatus(str, Enum):
    SUCCESS = "MS-01-00"


# @router.post("/demo")
# def create_iris(
#     request: Request,
#     rdb: Session = Depends(RDB_POOLS["pg_demo"].get_session_with_commit)
# ):
#     rdb.query()
#     result = "hello"
#     return response_generator(status.HTTP_200_OK, IrisStatus.SUCCESS, f"Created {len(result)} iris records", result)


@router.post("/")
def create_iris(
    request: Request,
    body: template.IrisCreateBatch,
    rdb: Session = Depends(RDB_POOLS["pg_demo"].get_session_with_commit)
):
    result = service.create_iris(rdb, body, request.headers)
    return response_generator(status.HTTP_200_OK, IrisStatus.SUCCESS, f"Created {len(result)} iris records", result)


@router.post("/condition")
def get_iris_by_condition(
    request: Request,
    body: template.IrisQueryCondition,
    offset: int = Query(..., ge=0, description="Number of records to skip"),
    limit: int = Query(..., ge=1, le=1000, description="Number of records to return"),
    rdb: Session = Depends(RDB_POOLS["pg_demo"].get_session)
):
    total, result = service.get_iris_by_condition(rdb, body, offset, limit)
    return response_generator(
        status.HTTP_200_OK, IrisStatus.SUCCESS, f"Fetched {len(result)} iris records from {total} records", result
    )


@router.put("/")
def update_iris(
    request: Request,
    body: template.IrisUpdateBatch,
    rdb: Session = Depends(RDB_POOLS["pg_demo"].get_session_with_commit)
):
    service.update_iris(rdb, body, request.headers)
    return response_generator(
        status.HTTP_200_OK, IrisStatus.SUCCESS, f"Updated {len(body.samples)} iris records", list()
    )


@router.put("/id")
def delete_iris(
    request: Request,
    body: template.IrisDelete,
    rdb: Session = Depends(RDB_POOLS["pg_demo"].get_session_with_commit)
):
    deleted_count = service.delete_iris(rdb, body)
    return response_generator(status.HTTP_200_OK, IrisStatus.SUCCESS, f"Deleted {deleted_count} iris records", list())


# @router.post("/predict")
# def predict_iris(
#     request: Request,
#     body: template.IrisPredict,
#     rdb: Session = Depends(RDB_POOLS["pg_demo"].get_session),
#     obs: HisObsClient = Depends(OBS_POOLS["demo_bucket"].get_session)
# ):
#     predictions, report_url = service.predict_iris(rdb, obs, body, request.headers)
#     return response_generator(status.HTTP_200_OK, IrisStatus.SUCCESS, report_url, predictions)
