from datetime import datetime
from typing import Annotated, List, Optional

from pydantic import BaseModel, Field, field_validator, UUID4

iris_species = Annotated[
    Optional[str],
    Field(
        default=None,
        min_length=1,
        max_length=20,
        pattern=r"^[a-z]+$",
        description="Species of the iris, must contain only lowercase letters"
    )
]


class Iris(BaseModel):
    sepal_length: Optional[float] = Field(None, ge=0, le=10, description="Sepal length in cm")
    sepal_width: Optional[float] = Field(None, ge=0, le=10, description="Sepal width in cm")
    petal_length: Optional[float] = Field(None, ge=0, le=10, description="Petal length in cm")
    petal_width: Optional[float] = Field(None, ge=0, le=10, description="Petal width in cm")
    species: iris_species


class IrisCreateBatch(BaseModel):
    samples: List[Iris] = Field(..., min_length=1, max_length=100, description="List of Iris samples to create")


class IrisUpdate(Iris):
    id: UUID4 = Field(..., description="Iris ID")


class IrisUpdateBatch(BaseModel):
    samples: List[IrisUpdate] = Field(..., min_length=1, max_length=100, description="List of Iris samples to update")


class IrisQueryCondition(BaseModel):
    ids: Optional[List[UUID4]] = Field(None, min_length=1, max_length=100, description="List of Iris IDs to filter")
    sepal_length_min: Optional[float] = Field(None, ge=0, le=10, description="Minimum sepal length")
    sepal_length_max: Optional[float] = Field(None, ge=0, le=10, description="Maximum sepal length")
    sepal_width_min: Optional[float] = Field(None, ge=0, le=10, description="Minimum sepal width")
    sepal_width_max: Optional[float] = Field(None, ge=0, le=10, description="Maximum sepal width")
    petal_length_min: Optional[float] = Field(None, ge=0, le=10, description="Minimum petal length")
    petal_length_max: Optional[float] = Field(None, ge=0, le=10, description="Maximum petal length")
    petal_width_min: Optional[float] = Field(None, ge=0, le=10, description="Minimum petal width")
    petal_width_max: Optional[float] = Field(None, ge=0, le=10, description="Maximum petal width")
    species: Optional[List[iris_species]] = Field(
        None, min_length=1, max_length=5, description="List of species of the iris"
    )
    labeled: Optional[bool] = Field(None, description="Has species label or not")
    creation_date_start: Optional[datetime] = Field(None, description="Start of creation date range")
    creation_date_end: Optional[datetime] = Field(None, description="End of creation date range")
    last_update_date_start: Optional[datetime] = Field(None, description="Start of last update date range")
    last_update_date_end: Optional[datetime] = Field(None, description="End of last update date range")

    @field_validator("species")
    @classmethod
    def validate_species(cls, v):
        if v is None:
            return v
        if any(item is None for item in v):
            raise ValueError("Species list cannot contain None values")
        cleaned_species = list(set(v))
        return cleaned_species


class IrisDelete(BaseModel):
    ids: List[UUID4] = Field(..., max_length=1000, description="List of Iris IDs to delete")


class IrisPredict(BaseModel):
    data: List[Iris] = Field(..., max_length=1000, description="List of Iris data to predict")
    train_samples: int = Field(..., ge=100, le=10000, description="Number of recent samples to use for training")
