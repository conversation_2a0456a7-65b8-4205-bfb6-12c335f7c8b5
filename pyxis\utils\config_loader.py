import os
from typing import Dict

from his_decrypt import EncryptType

from pyxis.utils.config_center.his_config_center_client import decrypt, HisConfigCenterClient, HisConfigCenterCredential
from pyxis.utils.config_template import <PERSON><PERSON>num, Config, InfraEnum
from pyxis.utils.logger.logger import get_base_logger
from pyxis.utils.obs.obs_client_pool import ObsClientPool
from pyxis.utils.rdb.sqlalchemy_pool import SQLAlchemyPool


class ConfigLoader:
    def __init__(self, config: dict):
        self.config = Config(**config)
        self.client = self.get_config_center_client()

    def get_config_center_client(self):
        if self.config.deploy.cloud == CloudEnum.HIS:
            credential = HisConfigCenterCredential(
                url=self.config.deploy.config_server.url,
                static_token=decrypt(
                    config_parts=self.config.deploy.config_server.config_parts,
                    work_key_cipher=os.environ.get("WORK_KEY_CIPHER", str()),
                    text=os.environ.get("STATIC_TOKEN", str()),
                    encrypt_type=EncryptType.ADV_2_6
                ),
                app_id=self.config.deploy.config_server.app_id,
                deployment_unit=self.config.deploy.config_server.du,
                region=self.config.deploy.config_server.region,
                environment=self.config.deploy.config_server.environment
            )
            return HisConfigCenterClient(self.config.deploy.api_gateway.authorization.url, credential)
        else:
            raise ValueError("Unknown Cloud Platform!")

    def get_rdb_pools(self) -> Dict[str, SQLAlchemyPool]:
        pools = dict()
        for db_name, db_config in self.config.rdb.items():
            pools[db_name] = SQLAlchemyPool(
                **self.client.get_rdb_config_by_name(datasource_name=db_config.datasource_name),
                **dict(db_config.model_dump(include={"tables", "echo"}))
            )

        return pools

    def get_obs_pools(self) -> Dict[str, ObsClientPool]:
        pools = dict()
        for bucket_name, bucket_config in self.config.obs.items():
            pools[bucket_name] = ObsClientPool(
                **{
                    **self.client.get_obs_config_by_bucket(bucket_config.bucket),
                    **dict(bucket_config.model_dump(exclude={"bucket"}))
                }
            )

        return pools

    def get_logger(self):
        if self.config.deploy.infra == InfraEnum.AIF:
            from aipaas.logger_factory import get_logger
            logger = get_logger(__name__, level=self.config.logger.level)
            logger.warn("When using AIF logger, only level config will be set")
            return logger
        else:
            return get_base_logger(**self.config.logger.model_dump())
