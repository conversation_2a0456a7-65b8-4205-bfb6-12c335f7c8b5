#!/bin/sh

echo "Python Server starting........................"
binPath=$(dirname $0)
cd $binPath

mkdir /applog/logs

yum -y install hostname

# 安装脚手架依赖包
pip3 install --index-url https://cmc-cd-mirror.rnd.huawei.com/pypi/simple/ --extra-index-url https://cmc.centralrepo.rnd.huawei.com/artifactory/product_pypi/simple --trusted-host cmc-cd-mirror.rnd.huawei.com --trusted-host cmc.centralrepo.rnd.huawei.com -r pyxis/requirements.txt


# 安装项目依赖包
pip3 install -r requirements.txt


if [ "$ENV" = "PROD-ADS" ] || [ "$ENV" = "PROD-AIF" ]; then
# 安装高斯DB专用驱动
pip3 uninstall psycopg2 -y
pip3 uninstall psycopg2-binary -y
wget https://cmc.centralrepo.rnd.huawei.com/artifactory/api/pypi/pypi-oss/psycopg2/2.9.6rc0+h0.csi.gaussdb.kernel.euleros2.10.r1/psycopg2-2.9.6rc0+h0.csi.gaussdb_kernel.euleros2.10.r1-py3-none-linux_x86_64.whl
unzip psycopg2-2.9.6rc0+h0.csi.gaussdb_kernel.euleros2.10.r1-py3-none-linux_x86_64.whl
cp psycopg2 $(python3 -c 'import site; print(site.getsitepackages()[0])') -r
chmod 755 $(python3 -c 'import site; print(site.getsitepackages()[0])')/psycopg2 -R
export PYTHONPATH=$(python3 -c 'import site; print(site.getsitepackages()[0])'):$PYTHONPATH
mkdir -p /usr/local/gaussdb
cp lib  /usr/local/gaussdb/ -r
chmod 755 -R /usr/local/gaussdb
export LD_LIBRARY_PATH=/usr/local/gaussdb/lib:$LD_LIBRARY_PATH
echo 'export LD_LIBRARY_PATH=/usr/local/gaussdb/lib:$LD_LIBRARY_PATH'>> /etc/profile
fi


# 启动Web服务进程
if [ "$ENV" = "DEV-AIF" ] || [ "$ENV" = "PROD-AIF" ]; then
    # AIF开启APM参数配置
    export SW_ENABLE="true"
    export SW_AGENT_PROJECT_ID="com.huawei.bi.aiservice.anaportal"
    export SW_AGENT_NAME="mmae-pyxis"
    export SW_AGENT_PROFILE_ACTIVE="false"
    export SW_AGENT_METER_REPORTER_ACTIVE="false"
    export SW_AGENT_LOG_REPORTER_ACTIVE="false"
    export SW_AGENT_PCLOUD_ENV_ALIAS="dev"
    export SW_AGENT_PROTOCOL="kafka"

    export GF_PLUGINS="apm"
    export GF_APM='{"SW_AGENT_PROJECT_ID":"com.huawei.bi.aiservice.anaportal","SW_AGENT_NAME":"mmae-pyxis"}'

    if [ "$ENV" = "PROD-AIF" ]; then
        export SW_KAFKA_BOOTSTRAP_SERVERS="apm-middle-huaji-kafka1.his.huawei.com:9092"
        export SW_AGENT_COLLECTOR_BACKEND_SERVICES="apm-middle-huaji-handler1.his.huawei.com:30612"
        curl -k https://apig.his.huawei.com/api/get/pictureinfo/agent.apm-python-for-AIF.sh  -o ./agent.apm-python-for-AIF.sh  &&  chmod 775  ./agent.apm-python-for-AIF.sh
    else
        export SW_KAFKA_BOOTSTRAP_SERVERS="apm20-kafka1.hisuat.huawei.com:9092"
        export SW_AGENT_COLLECTOR_BACKEND_SERVICES="apm20-agent2.hisuat.huawei.com:31002"
        curl -k https://apig-beta.his.huawei.com/api/get/pictureinfo/agent.apm-python-for-AIF.sh  -o ./agent.apm-python-for-AIF.sh  &&  chmod 775  ./agent.apm-python-for-AIF.sh
    fi

    source agent.apm-python-for-AIF.sh

    sw-python run uvicorn main:app --host 0.0.0.0 --port 80 --workers 1 &
elif [ "$ENV" = "DEV-ADS" ] || [ "$ENV" = "PROD-ADS" ]; then
    # ADS开启APM需选择镜像python3.9.11 (202309V1)，不开启APM则去掉sw-python run直接使用uvicorn启动
    sh /script/apm.sh
    source $HOME/.bashrc

    sw-python run uvicorn main:app --host 0.0.0.0 --port 8080 --workers 1 &
else
    uvicorn main:app --host 0.0.0.0 --port 8080 --workers 1 &
fi


while :
do
 sleep 1
done
