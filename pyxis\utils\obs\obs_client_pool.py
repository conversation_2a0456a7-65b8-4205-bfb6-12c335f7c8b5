from contextlib import contextmanager

from pyxis.utils.obs.his_obs_client_pool import HisObsClientPool

PLATFORM_POOL_MAP = {
    "HIS": HisObsClientPool
}


class ObsClientPool:
    def __init__(
        self,
        cloud: str = "HIS",
        pool_size: int = 5,
        max_overflow: int = 5,
        pool_timeout: float = 2.0,
        pool_recycle: float = 60.0 * 10,
        pool_pre_ping: bool = False,
        *args, **kwargs
    ):
        self.pool = PLATFORM_POOL_MAP.get(cloud, HisObsClientPool)(
            pool_size=pool_size,
            max_overflow=max_overflow,
            pool_timeout=pool_timeout,
            pool_recycle=pool_recycle,
            pool_pre_ping=pool_pre_ping,
            *args, **kwargs
        )

    def get_session(self):
        obs_client = self.pool.get_session()
        try:
            yield obs_client
        finally:
            self.pool.put_session(obs_client)

    @contextmanager
    def context_session(self):
        obs_client = self.pool.get_session()
        try:
            yield obs_client
        finally:
            self.pool.put_session(obs_client)

    def release(self):
        self.pool.release()
