{"DEPLOY": {"cloud": "HIS", "infra": "AIF", "enable_doc": false, "service_name": "", "uri_prefix": "/mmae-pyxis", "config_server": {"url": "http://appconfig.his-op.huawei.com/ConfigCenter/services/saasConfigcenterGetConfig?application_id=904eef23d0874c1da63b14d6b1c9e946&sub_application_id=fcst_profits_service&region=kwe4hwsimilar&environment=similar-general&version=1.0", "app_id": "com.huawei.bi.aiservice.anaportal", "du": "scaffolding", "environment": "kwe_prod", "region": "kwe", "version": "1.0", "config_parts": ["替换成生产token解密要的config parts 1", "替换成生产token解密要的config parts 2"]}, "api_gateway": {"authorization": {"url": "http://w3cloud.huawei.com/ApiCommonQuery/appToken/getRestAppDynamicToken"}, "authentication": {"sgov_token": "http://w3cloud.huawei.com/ApiCommonQuery/appToken/validateToken", "sgov_uri": "http://w3cloud.huawei.com/ApiCommonQuery/appToken/authorizeByURI"}}}, "LOGGER": {"level": "INFO"}, "RDB": {"pg_demo": {"datasource_name": "pyxis.pg", "tables": ["iris"], "echo": false}}, "OBS": {"demo_bucket": {"bucket": "fai-mlops-demo", "pool_size": 5, "max_overflow": 5, "pool_timeout": 2, "pool_recycle": 3600, "pool_pre_ping": true}}, "LIMITER": {"storage_uri": null, "strategy": "moving-window", "unit": "minute", "frequency": 10}}