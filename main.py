from fastapi import Request, status
from fastapi.exceptions import RequestValidationError
from pydantic import ValidationError
from slowapi.errors import RateLimitExceeded
from slowapi.middleware import SlowAPIMiddleware
from starlette.middleware.gzip import GZipMiddleware

from pyxis.utils.app_factory import create_app
from src.iris import router as iris_router
from src.utils import middleware
from src.utils.resource_loader import CONFIG, LIMITER, RDB_POOLS
from src.utils.response_generator import response_generator

app = create_app(
    infra=CONFIG.deploy.infra,
    enable_doc=CONFIG.deploy.enable_doc,
    service_name=CONFIG.deploy.service_name,
    uri_prefix=CONFIG.deploy.uri_prefix
)


# 添加启动、关闭处理函数，释放资源
def startup():
    for pool in RDB_POOLS.values():
        pool.reflect()


def shutdown():
    for pool in RDB_POOLS.values():
        pool.release()
    # for pool in OBS_POOLS.values():
    #     pool.release()


app.add_event_handler("startup", startup)
app.add_event_handler("shutdown", shutdown)

# 添加限流器
app.state.limiter = LIMITER

# 挂载子服务
app.include_router(iris_router.router)

# 添加中间件函数、异常处理函数
app.add_exception_handler(RequestValidationError, middleware.validation_error_handler)
app.add_exception_handler(ValidationError, middleware.validation_error_handler)
app.add_exception_handler(RateLimitExceeded, middleware.rate_limit_exceeded_handler)
app.add_middleware(GZipMiddleware, minimum_size=1000)
app.add_middleware(middleware.AuthenticationMiddleware)
app.add_middleware(middleware.RequestMiddleware)
app.add_middleware(SlowAPIMiddleware)
app.add_middleware(middleware.ExceptionMiddleware)
app.add_middleware(middleware.TimingMiddleware)
app.add_middleware(middleware.AuditLoggingMiddleware)



@app.get(f"{CONFIG.deploy.service_name}/test", include_in_schema=False)
@LIMITER.exempt
def health_check(
    request: Request,
):
    """
    健康检测
    """
    return response_generator(
        status.HTTP_200_OK, middleware.AppBizStatus.SUCCESS, middleware.AppBizStatusMsg.HEALTH_TEST, list()
    )
