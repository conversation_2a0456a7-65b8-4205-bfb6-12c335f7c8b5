from contextlib import contextmanager
from typing import Any, Dict, List, Optional, Union

from sqlalchemy import create_engine
from sqlalchemy.engine import URL
from sqlalchemy.ext.automap import automap_base
from sqlalchemy.orm import scoped_session, sessionmaker
from sqlalchemy.pool import QueuePool


class SQLAlchemyPool:
    def __init__(
        self,
        driver_name: str,
        user: Optional[str] = None,
        password: Optional[str] = None,
        host: Optional[str] = None,
        port: Optional[str] = None,
        database: Optional[str] = None,
        connect_args: Dict[str, Any] = None,
        pool_class: Any = QueuePool,
        pool_size: int = 5,
        max_overflow: int = 5,
        pool_timeout: float = 2.0,
        pool_recycle: float = 60.0 * 10,
        pool_pre_ping: bool = True,
        pool_use_lifo: bool = True,
        tables: List[str] = None,
        echo: Union[bool, str] = False
    ):
        self._schema = connect_args.pop("schema", None)
        self._tables = tables if tables is not None else list()
        self._engine = create_engine(
            url=URL.create(
                drivername=driver_name,
                username=user,
                password=password,
                host=host,
                port=port,
                database=database
            ),
            poolclass=pool_class,
            pool_size=pool_size,
            max_overflow=max_overflow,
            pool_timeout=pool_timeout,
            pool_recycle=pool_recycle,
            echo=echo,
            echo_pool=echo,
            pool_pre_ping=pool_pre_ping,
            pool_use_lifo=pool_use_lifo,
            connect_args=connect_args if connect_args is not None else dict()
        )
        self._Session = sessionmaker(autocommit=False, bind=self._engine)
        self.Base = automap_base()

    def reflect(self):
        self.Base.prepare(
            self._engine, reflect=True, schema=self._schema,
            reflection_options={"only": self._tables}
        )

    def get_session(self):
        session = scoped_session(self._Session)
        try:
            yield session
        finally:
            session.close()

    def get_session_with_commit(self):
        session = scoped_session(self._Session)
        try:
            yield session
            session.commit()
        finally:
            session.close()

    @contextmanager
    def context_session(self, commit: bool = False):
        session = scoped_session(self._Session)
        try:
            with session.connection():
                yield session
                if commit:
                    session.commit()
        finally:
            session.close()

    def release(self):
        self._engine.dispose()
