from sqlalchemy import Column, DateTime, Float, String

from src.utils.resource_loader import RDB_POOLS


def create_iris_class(base):
    class _Iris(base):
        __tablename__ = "iris"

        id = Column(String(50), primary_key=True)
        sepal_length = Column(Float)
        sepal_width = Column(Float)
        petal_length = Column(Float)
        petal_width = Column(Float)
        species = Column(String(50))
        created_by = Column(String(100), nullable=False)
        creation_date = Column(DateTime(timezone=True), nullable=False)
        last_updated_by = Column(String(100), nullable=False)
        last_update_date = Column(DateTime(timezone=True), nullable=False)

        def __repr__(self):
            return """
            Iris(
                id = '{self.id}',
                sepal_length = '{self.sepal_length}',
                sepal_width = '{self.sepal_width}',
                petal_length = '{self.petal_length}',
                species = '{self.species}',
                created_by = '{self.created_by}',
                creation_date = '{self.creation_date}',
                last_updated_by = '{self.last_updated_by}',
                last_update_date = '{self.last_update_date}',
            )
            """.format(self=self)

    return _Iris


Iris = create_iris_class(RDB_POOLS["pg_demo"].Base)
