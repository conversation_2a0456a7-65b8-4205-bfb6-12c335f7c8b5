from fastapi import FastAPI
from fastapi.openapi.docs import get_redoc_html, get_swagger_ui_html

from pyxis.utils.config_template import InfraEnum


def _aif_swagger_ui_route(service_name: str, uri_prefix: str):
    async def custom_swagger_ui_html():
        return get_swagger_ui_html(
            openapi_url=f"{uri_prefix}/openapi.json",
            title=f"{service_name.lstrip('/').upper()} Micro Service - Swagger UI"
        )

    return custom_swagger_ui_html


def _aif_redoc_route(service_name: str, uri_prefix: str):
    async def redoc_html():
        return get_redoc_html(
            openapi_url=f"{uri_prefix}/openapi.json",
            title=f"{service_name.lstrip('/').upper()} Micro Service - ReDoc"
        )

    return redoc_html


def _create_aif_app(service_name: str, uri_prefix: str):
    aif_app = FastAPI(
        title=f"{service_name.lstrip('/').upper()} Micro Service",
        openapi_url="/openapi.json",
        docs_url=None,
        redoc_url=None
    )

    aif_app.add_api_route("/docs", _aif_swagger_ui_route(service_name, uri_prefix), include_in_schema=False)
    aif_app.add_api_route("/redoc", _aif_redoc_route(service_name, uri_prefix), include_in_schema=False)

    return aif_app


def _create_default_app(service_name: str):
    return FastAPI(
        title=f"{service_name.lstrip('/').upper()} Micro Service",
        openapi_url=f"{service_name}/openapi.json",
        docs_url=f"{service_name}/docs",
        redoc_url=f"{service_name}/redoc"
    )


def create_app(
    infra: str = str(), enable_doc: bool = True, service_name: str = str(), uri_prefix: str = str()
) -> FastAPI:
    if infra == InfraEnum.AIF:
        app = _create_aif_app(service_name, uri_prefix)
    else:
        app = _create_default_app(service_name)

    if not enable_doc:
        app.openapi_url = None
        app.docs_url = None
        app.redoc_url = None

    return app
