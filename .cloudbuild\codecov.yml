version: 2.0

env:
  resource:
    type: docker
    image: szvecr03.his.huawei.com:80/ecr-build-green/codecov_go_python_node:latest
    class: 8U16G
    pool: eks-build-x86-gz-kunpeng-green-ondocker-16u-02

#单元测试步骤
steps:
  PRE_BUILD: # 构建准备:下载代码
    - checkout
  BUILD: # 构建执行
    - build_execute:
        command: |
          # 切换到项目根目录         
          cd ${WORKSPACE}
          export PYTHONPATH="${PYTHONPATH}:${pwd}"
          # 安装pytest相关的依赖包
          pip3 install -r pyxis/requirements.txt --index-url https://cmc-cd-mirror.rnd.huawei.com/pypi/simple/ --extra-index-url https://cmc.centralrepo.rnd.huawei.com/artifactory/product_pypi/simple --trusted-host cmc-cd-mirror.rnd.huawei.com --trusted-host cmc.centralrepo.rnd.huawei.com
          pip3 install -r requirements.txt --index-url https://cmc-cd-mirror.rnd.huawei.com/pypi/simple/ --extra-index-url https://cmc.centralrepo.rnd.huawei.com/artifactory/product_pypi/simple --trusted-host cmc-cd-mirror.rnd.huawei.com --trusted-host cmc.centralrepo.rnd.huawei.com
          pip3 install six pytest pytest-html pytest-cov pytest-metadata mock coverage --index-url https://cmc-cd-mirror.rnd.huawei.com/pypi/simple/ --extra-index-url https://cmc.centralrepo.rnd.huawei.com/artifactory/product_pypi/simple --trusted-host cmc-cd-mirror.rnd.huawei.com --trusted-host cmc.centralrepo.rnd.huawei.com 

          # 启动单元测试
          pytest tests --cov=pyxis --cov-report=html:reports/coverage_report --cov-config=tests/.coveragerc --html=reports/testresult.html --self-contained-html
        check: false
  POST_BUILD:
    - sh:
        command: |
             cd ${WORKSPACE} 
             ls
             curl -k "https://codecov.rnd.huawei.com/rest/codecov/freeExecShell/getShContent/1821462595949821953" -o codecov.sh
             bash -x codecov.sh
