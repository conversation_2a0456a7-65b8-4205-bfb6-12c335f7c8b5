#!/bin/bash

# 获取jinkins的$buildNumber，获取时间戳，获取build随机数
if [ -z ${buildNumber} ];then
    if [ -e /proc/sys/kernel/random/uuid ] && [ -r /proc/sys/kernel/random/uuid ];then
        build=`cat /proc/sys/kernel/random/uuid| cksum | cut -f1 -d" "`
    else
        build=${RANDOM}
    fi
    buildNumber="${build}"
else
    buildNumber="${buildNumber}"
fi
# 微服务名称
SERVICE_NAME="fai.demo"


echo "Release is ${isRelease}"
# 判断当前构建是否为版本构建，以及定义构建变量(包版本,包服务名称,包编译存放路径,包类型,包编译名称,包打包名称)
if [ "${isRelease}"x = "false"x ];then
    SERVICE_VERSION="1.0.0-SNAPSHOT"
    # 版本号+build随机数写入buildInfo.properties
    echo "buildVersion=${SERVICE_VERSION}.$buildNumber">buildInfo.properties

    # 执行工程编译
    workdir=$(cd $(dirname $0); pwd)
    cd $workdir
    #覆盖云龙流水线默认构建仓库地址
    pip3 config set global.index-url "https://cmc.centralrepo.rnd.huawei.com/artifactory/pypi-central-repo/simple/"
    # 压缩打包命令
    tar -zcvf ${SERVICE_NAME}.tar.gz --exclude=.cloudbuild --exclude=tests --exclude=build.sh *  #*代表所有，自行修改。-x是排除的命令,如果排除的目录是也是目录,需要加“”。如果只是一个文件，则直接写文件路径.
    mkdir ${SERVICE_NAME}
    tar -xvf ${SERVICE_NAME}.tar.gz -C ${SERVICE_NAME}
    rm -rf ${SERVICE_NAME}.tar.gz
    tar -zcvf ${SERVICE_NAME}.tar.gz ${SERVICE_NAME}
	ls
elif [ "${isRelease}"x = "true"x ];then
    SERVICE_VERSION=${releaseVersion}
    # 版本号+build随机数写入buildInfo.properties
    echo "buildVersion=${SERVICE_VERSION}">buildInfo.properties

    # 执行工程编译
    workdir=$(cd $(dirname $0); pwd)
    cd $workdir
    #覆盖云龙流水线默认构建仓库地址
    pip3 config set global.index-url "https://cmc.centralrepo.rnd.huawei.com/artifactory/pypi-central-repo/simple/"
    # 压缩打包命令
    tar -zcvf ${SERVICE_NAME}.tar.gz --exclude=.cloudbuild --exclude=tests --exclude=build.sh *  #*代表所有，自行修改。-x是排除的命令,如果排除的目录是也是目录,需要加“”。如果只是一个文件，则直接写文件路径.
    mkdir ${SERVICE_NAME}
    tar -xvf ${SERVICE_NAME}.tar.gz -C ${SERVICE_NAME}
    rm -rf ${SERVICE_NAME}.tar.gz
    tar -zcvf ${SERVICE_NAME}.tar.gz ${SERVICE_NAME}
	ls
fi
