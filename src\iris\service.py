import io
import json
import uuid
from datetime import datetime, timezone

import numpy as np
import pandas as pd
import pytz
from fastapi import Request
from sklearn import tree
from sklearn.metrics import accuracy_score
from sklearn.model_selection import train_test_split
from sqlalchemy.orm import Session

from pyxis.utils.obs.his_obs_client import HisObsClient
from src.iris import template
from src.iris.model import <PERSON>


def create_iris(rdb: Session, body: template.IrisCreateBatch, headers: Request.headers):
    new_iris = [
        Iris(
            id=str(uuid.uuid4()),
            sepal_length=iris.sepal_length,
            sepal_width=iris.sepal_width,
            petal_length=iris.petal_length,
            petal_width=iris.petal_width,
            species=iris.species,
            created_by=headers.get("X-USERID"),
            creation_date=datetime.now(timezone.utc),
            last_updated_by=headers.get("X-USERID"),
            last_update_date=datetime.now(timezone.utc)
        )
        for iris in body.samples
    ]
    rdb.add_all(new_iris)
    return [iris.id for iris in new_iris]


def add_query_filter(query, body):
    if body.ids:
        query = query.filter(Iris.id.in_([str(iris_id) for iris_id in body.ids]))
    if body.sepal_length_min:
        query = query.filter(Iris.sepal_length >= body.sepal_length_min)
    if body.sepal_length_max:
        query = query.filter(Iris.sepal_length <= body.sepal_length_max)
    if body.sepal_width_min:
        query = query.filter(Iris.sepal_width >= body.sepal_width_min)
    if body.sepal_width_max:
        query = query.filter(Iris.sepal_width <= body.sepal_width_max)
    if body.petal_length_min:
        query = query.filter(Iris.petal_length >= body.petal_length_min)
    if body.petal_length_max:
        query = query.filter(Iris.petal_length <= body.petal_length_max)
    if body.petal_width_min:
        query = query.filter(Iris.petal_width >= body.petal_width_min)
    if body.petal_width_max:
        query = query.filter(Iris.petal_width <= body.petal_width_max)
    if body.species:
        query = query.filter(Iris.species.in_(body.species))
    if body.labeled is not None:
        if body.labeled is True:
            query = query.filter(Iris.species.is_not(None))
        else:
            query = query.filter(Iris.species.is_(None))
    if body.creation_date_start:
        query = query.filter(Iris.creation_date >= body.creation_date_start)
    if body.creation_date_end:
        query = query.filter(Iris.creation_date <= body.creation_date_end)
    if body.last_update_date_start:
        query = query.filter(Iris.last_update_date >= body.last_update_date_start)
    if body.last_update_date_end:
        query = query.filter(Iris.last_update_date <= body.last_update_date_end)
    return query


def get_iris_by_condition(rdb: Session, body: template.IrisQueryCondition, offset: int, limit: int):
    query = rdb.query(
        Iris.id,
        Iris.sepal_length, Iris.sepal_width, Iris.petal_length, Iris.petal_width, Iris.species,
        Iris.created_by, Iris.creation_date, Iris.last_updated_by, Iris.last_update_date
    )

    query = add_query_filter(query, body)
    total = query.count()
    query = query.offset(offset).limit(limit)
    data = pd.read_sql_query(query.statement, rdb.bind)

    data.creation_date = data.creation_date.dt.tz_convert(datetime.now(pytz.utc).astimezone().tzinfo)
    data.creation_date = data.creation_date.apply(lambda x: x.strftime("%Y-%m-%d %H:%M:%S"))

    data.last_update_date = data.last_update_date.dt.tz_convert(datetime.now(pytz.utc).astimezone().tzinfo)
    data.last_update_date = data.last_update_date.apply(lambda x: x.strftime("%Y-%m-%d %H:%M:%S"))

    data.replace({np.nan: None, pd.NaT: None}, inplace=True)

    return total, data.to_dict(orient="records")


def update_iris(rdb: Session, body: template.IrisUpdateBatch, headers: Request.headers):
    for iris in body.samples:
        rdb.query(
            Iris
        ).filter(
            Iris.id == str(iris.id)
        ).update(
            {
                Iris.sepal_length: iris.sepal_length,
                Iris.sepal_width: iris.sepal_width,
                Iris.petal_length: iris.petal_length,
                Iris.petal_width: iris.petal_width,
                Iris.species: iris.species,
                Iris.last_updated_by: headers.get("X-USERID"),
                Iris.last_update_date: datetime.now(timezone.utc)
            }
        )


def delete_iris(rdb: Session, body: template.IrisDelete):
    deleted = rdb.query(Iris).filter(
        Iris.id.in_([str(iris_id) for iris_id in body.ids])
    ).delete()
    return deleted


def predict_iris(rdb: Session, obs: HisObsClient, body: template.IrisPredict, headers: Request.headers):
    # Fetch training data
    query = rdb.query(
        Iris.sepal_length, Iris.sepal_width, Iris.petal_length, Iris.petal_width, Iris.species
    ).limit(
        body.train_samples
    )
    iris_dataset = pd.read_sql_query(query.statement, rdb.bind)

    # Prepare data
    x = ["sepal_length", "sepal_width", "petal_length", "petal_width"]
    y = ["species"]
    iris_dataset.dropna(subset=y)
    iris_dataset.loc[:, x] = iris_dataset[x].fillna(iris_dataset[x].mean())
    iris_dataset[y] = iris_dataset[y].where(
        iris_dataset[y].isin(["setosa", "versicolor", "virginica"]), "unknown"
    )
    x_train, x_test, y_train, y_test = train_test_split(iris_dataset[x], iris_dataset[y], test_size=.3)

    # Train model
    classifier = tree.DecisionTreeClassifier()
    classifier.fit(x_train, y_train)
    predictions = classifier.predict(x_test)

    # Prepare prediction data
    x_pred = pd.DataFrame([item.model_dump() for item in body.data])[x]
    x_pred.loc[:, x] = x_pred[x].fillna(x_pred[x].mean())

    # Make predictions
    y_hat = classifier.predict(x_pred)

    # Generate Report
    report = {
        "Num of Raw Training Samples": body.train_samples,
        "Num of Labeled Training Samples": len(iris_dataset),
        "Train Model Accuracy": round(accuracy_score(y_test, predictions), 2),
        "Raw Input Samples": [item.model_dump() for item in body.data],
        "Processed Input Samples": x_pred[x].to_dict(orient="records"),
        "Predictions": y_hat.tolist()
    }

    # Save Report
    file = io.StringIO()
    json.dump(report, file, ensure_ascii=False)
    obs.upload_object_from_memory(
        object_key=f"{headers.get('X-APPID')}/{headers.get('X-USERID')}/{headers.get('X-TRACEID')}.json",
        data=file.getvalue()
    )
    url = obs.get_object_download_url(
        object_key=f"{headers.get('X-APPID')}/{headers.get('X-USERID')}/{headers.get('X-TRACEID')}.json"
    )

    return y_hat.tolist(), url
