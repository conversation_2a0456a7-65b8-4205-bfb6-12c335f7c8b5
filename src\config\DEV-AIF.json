{"DEPLOY": {"cloud": "HIS", "infra": "AIF", "enable_doc": true, "service_name": "", "uri_prefix": "/mmae-pyxis", "config_server": {"url": "http://appconfig-beta.huawei.com/ConfigCenter/services/saasConfigcenterGetConfig", "app_id": "com.huawei.bi.aiservice.anaportal", "du": "scaffolding", "environment": "kwe_dev", "region": "kwe", "version": "1.0", "config_parts": ["c61592b09fc24b5a9e967f5532b92364", "db670e9972654526a04010055145872b"]}, "api_gateway": {"authorization": {"url": "http://kwe-beta.huawei.com/ApiCommonQuery/appToken/getRestAppDynamicToken"}, "authentication": {"sgov_token": "http://kwe-beta.huawei.com/ApiCommonQuery/appToken/validateToken", "sgov_uri": "http://kwe-beta.huawei.com/ApiCommonQuery/appToken/authorizeByURI"}}}, "LOGGER": {"level": "DEBUG"}, "RDB": {"pg_demo": {"datasource_name": "pyxis.pg", "tables": ["iris"], "echo": true}}, "OBS": {"demo_bucket": {"bucket": "fai-mlops-demo", "pool_size": 5, "max_overflow": 5, "pool_timeout": 2, "pool_recycle": 3600, "pool_pre_ping": true}}, "LIMITER": {"storage_uri": null, "strategy": "moving-window", "unit": "minute", "frequency": 10}}