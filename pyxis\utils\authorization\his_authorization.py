import base64
import json

import requests
from cachetools.func import ttl_cache
from tenacity import retry, stop_after_attempt, wait_fixed, retry_if_not_exception_type


class HisAuthorizationError(Exception):
    def __init__(self):
        super().__init__()


@ttl_cache(maxsize=128, ttl=300)
@retry(
    retry=retry_if_not_exception_type(HisAuthorizationError), stop=stop_after_attempt(3),
    wait=wait_fixed(1), reraise=True
)
def get_dynamic_token(url: str, app_id: str, static_token: str) -> str:
    info = {
        "appId": app_id,
        "credential": str(base64.b64encode(bytes(static_token, "utf-8")), "utf-8")
    }

    response = requests.post(
        url,
        json=info, headers={"Content-Type": "application/json"},
        timeout=1
    )
    if response.status_code != 200:
        raise HisAuthorizationError()
    result = json.loads(response.text)
    dynamic_token = result["result"]
    if dynamic_token is None:
        raise HisAuthorizationError()
    return dynamic_token
