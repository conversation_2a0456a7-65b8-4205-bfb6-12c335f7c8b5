from dataclasses import dataclass
from typing import Dict, Optional
from urllib.parse import parse_qsl, urlparse

DRIVER_MAPPING = {
    "postgresql": "postgresql+psycopg2",
    "opengauss": "opengauss+psycopg2"
}


@dataclass(frozen=True)
class RdbConnectionInfo:
    driver: str
    user: Optional[str]
    password: Optional[str]
    host: Optional[str]
    port: Optional[int]
    database: Optional[str]
    connect_args: Dict[str, str]


def parse_rdb_url(url: str) -> RdbConnectionInfo:
    """
    Parse database URL and return connection information for SQLAlchemy.

    URL format examples:
    - postgresql://user:password@host1,host2,host3:port/postgres?schema=public&target_session_attrs=primary
    - opengauss://user:password@host1,host2,host3:port/postgres?schema=public&target_session_attrs=primary
    """
    # if url start with 'jdbc:', remove it
    if url.startswith('jdbc:'):
        url = url[5:]
    parsed = urlparse(url)

    # 解析查询参数
    # connect_args = dict(parse_qsl(parsed.query))
    # if connection option  contains "stringtype", remove it
    # if "stringtype" in connect_args:
    #     del connect_args["stringtype"]
    # if "currentSchema" in connect_args:
    #     del connect_args["currentSchema"]
    connect_args = {"schema": "fin_dm_opt_tod", "target_session_attrs": "primary"}


    # 获取驱动类型
    scheme = parsed.scheme.lower()
    if scheme not in DRIVER_MAPPING:
        raise ValueError(f"Unsupported database type: {scheme}")

    return RdbConnectionInfo(
        driver=DRIVER_MAPPING[scheme],
        user=parsed.username,
        password=parsed.password,
        host=parsed.hostname,
        port=parsed.port,
        database=parsed.path.lstrip("/") if parsed.path else None,
        connect_args=connect_args
    )
